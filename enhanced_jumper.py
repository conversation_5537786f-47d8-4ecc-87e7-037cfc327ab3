import pygame
import asyncio
import platform
import random
import math
import json
from pathlib import Path

# Initialize Pygame
pygame.init()

# Screen settings
WIDTH, HEIGHT = 1000, 700
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("🦘 Pixel Jumper Adventure")
FPS = 60
clock = pygame.time.Clock()

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (0, 100, 255)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
RED = (255, 0, 0)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)
BROWN = (139, 69, 19)
GRAY = (128, 128, 128)

class ParticleSystem:
    def __init__(self):
        self.particles = []
    
    def add_particles(self, x, y, color, count=10):
        for _ in range(count):
            self.particles.append({
                'x': x + random.randint(-10, 10),
                'y': y + random.randint(-10, 10),
                'vx': random.uniform(-3, 3),
                'vy': random.uniform(-5, -1),
                'life': random.uniform(30, 60),
                'color': color,
                'size': random.randint(2, 5)
            })
    
    def update(self):
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['vy'] += 0.2  # gravity
            particle['life'] -= 1
            
            if particle['life'] <= 0:
                self.particles.remove(particle)
    
    def draw(self, surface):
        for particle in self.particles:
            alpha = particle['life'] / 60
            size = int(particle['size'] * alpha)
            if size > 0:
                pygame.draw.circle(surface, particle['color'], 
                                 (int(particle['x']), int(particle['y'])), size)

class Player(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = pygame.Surface((40, 40))
        self.image.fill(BLUE)
        self.rect = self.image.get_rect(topleft=(x, y))
        self.vel_y = 0
        self.vel_x = 0
        self.jump_power = -15
        self.gravity = 0.8
        self.on_ground = False
        self.max_speed = 5
        self.health = 100
        self.max_health = 100
        self.invulnerable = 0
        self.double_jump_available = True
        self.dash_cooldown = 0
        self.dash_power = 15
        
        # Animation
        self.bounce = 0
        self.trail = []
    
    def update(self, platforms, enemies, power_ups):
        # Handle invulnerability
        if self.invulnerable > 0:
            self.invulnerable -= 1
        
        # Handle dash cooldown
        if self.dash_cooldown > 0:
            self.dash_cooldown -= 1
        
        # Apply gravity
        self.vel_y += self.gravity
        
        # Horizontal movement with momentum
        keys = pygame.key.get_pressed()
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.vel_x = max(self.vel_x - 0.5, -self.max_speed)
        elif keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.vel_x = min(self.vel_x + 0.5, self.max_speed)
        else:
            # Apply friction
            if self.vel_x > 0:
                self.vel_x = max(0, self.vel_x - 0.3)
            else:
                self.vel_x = min(0, self.vel_x + 0.3)
        
        # Update position
        self.rect.x += self.vel_x
        self.rect.y += self.vel_y
        
        # Platform collision
        self.on_ground = False
        for platform in platforms:
            if self.rect.colliderect(platform.rect) and self.vel_y > 0:
                self.rect.bottom = platform.rect.top
                self.vel_y = 0
                self.on_ground = True
                self.double_jump_available = True
        
        # Enemy collision
        if self.invulnerable == 0:
            for enemy in enemies:
                if self.rect.colliderect(enemy.rect):
                    self.take_damage(20)
                    # Knockback
                    if enemy.rect.centerx < self.rect.centerx:
                        self.vel_x = 8
                    else:
                        self.vel_x = -8
                    self.vel_y = -10
        
        # Power-up collision
        for power_up in power_ups:
            if self.rect.colliderect(power_up.rect):
                power_up.apply_effect(self)
                power_ups.remove(power_up)
        
        # Screen boundaries
        if self.rect.bottom > HEIGHT:
            self.take_damage(50)  # Fall damage
            self.rect.bottom = HEIGHT
            self.vel_y = 0
            self.on_ground = True
        
        if self.rect.left < 0:
            self.rect.left = 0
            self.vel_x = 0
        if self.rect.right > WIDTH:
            self.rect.right = WIDTH
            self.vel_x = 0
        
        # Animation
        self.bounce = math.sin(pygame.time.get_ticks() * 0.01) * 2
        
        # Trail effect
        self.trail.append((self.rect.centerx, self.rect.centery))
        if len(self.trail) > 10:
            self.trail.pop(0)
    
    def jump(self):
        if self.on_ground:
            self.vel_y = self.jump_power
        elif self.double_jump_available:
            self.vel_y = self.jump_power * 0.8
            self.double_jump_available = False
    
    def dash(self):
        if self.dash_cooldown == 0:
            keys = pygame.key.get_pressed()
            if keys[pygame.K_LEFT] or keys[pygame.K_a]:
                self.vel_x = -self.dash_power
            elif keys[pygame.K_RIGHT] or keys[pygame.K_d]:
                self.vel_x = self.dash_power
            self.dash_cooldown = 60  # 1 second at 60 FPS
    
    def take_damage(self, amount):
        if self.invulnerable == 0:
            self.health = max(0, self.health - amount)
            self.invulnerable = 60  # 1 second of invulnerability
    
    def heal(self, amount):
        self.health = min(self.max_health, self.health + amount)
    
    def draw(self, surface):
        # Draw trail
        for i, pos in enumerate(self.trail):
            alpha = i / len(self.trail)
            size = int(5 * alpha)
            if size > 0:
                trail_color = (*BLUE, int(255 * alpha))
                pygame.draw.circle(surface, BLUE, pos, size)
        
        # Draw player with bounce and invulnerability effect
        draw_y = self.rect.y + self.bounce
        color = WHITE if self.invulnerable % 10 < 5 and self.invulnerable > 0 else BLUE
        
        player_rect = pygame.Rect(self.rect.x, draw_y, self.rect.width, self.rect.height)
        pygame.draw.rect(surface, color, player_rect)
        pygame.draw.rect(surface, BLACK, player_rect, 2)
        
        # Draw health bar
        health_width = 60
        health_height = 8
        health_x = self.rect.x - 10
        health_y = self.rect.y - 15
        
        # Background
        pygame.draw.rect(surface, RED, (health_x, health_y, health_width, health_height))
        # Health
        health_fill = (self.health / self.max_health) * health_width
        pygame.draw.rect(surface, GREEN, (health_x, health_y, health_fill, health_height))
        # Border
        pygame.draw.rect(surface, BLACK, (health_x, health_y, health_width, health_height), 1)

class Platform(pygame.sprite.Sprite):
    def __init__(self, x, y, width, platform_type="normal"):
        super().__init__()
        self.platform_type = platform_type
        self.image = pygame.Surface((width, 20))
        
        if platform_type == "normal":
            self.image.fill(GREEN)
        elif platform_type == "ice":
            self.image.fill((173, 216, 230))
        elif platform_type == "bouncy":
            self.image.fill(PURPLE)
        elif platform_type == "moving":
            self.image.fill(ORANGE)
            self.direction = random.choice([-1, 1])
            self.speed = 2
        
        self.rect = self.image.get_rect(topleft=(x, y))
        self.original_x = x
    
    def update(self):
        if self.platform_type == "moving":
            self.rect.x += self.direction * self.speed
            if self.rect.x <= self.original_x - 50 or self.rect.x >= self.original_x + 50:
                self.direction *= -1

class Enemy(pygame.sprite.Sprite):
    def __init__(self, x, y, enemy_type="walker"):
        super().__init__()
        self.enemy_type = enemy_type
        self.image = pygame.Surface((30, 30))
        
        if enemy_type == "walker":
            self.image.fill(RED)
            self.speed = 1
            self.direction = random.choice([-1, 1])
        elif enemy_type == "jumper":
            self.image.fill(ORANGE)
            self.speed = 2
            self.jump_timer = 0
        elif enemy_type == "flyer":
            self.image.fill(PURPLE)
            self.speed = 1.5
            self.float_offset = random.uniform(0, math.pi * 2)
        
        self.rect = self.image.get_rect(topleft=(x, y))
        self.original_y = y
    
    def update(self, platforms):
        if self.enemy_type == "walker":
            self.rect.x += self.direction * self.speed
            
            # Turn around at edges or walls
            if self.rect.left <= 0 or self.rect.right >= WIDTH:
                self.direction *= -1
            
            # Check for platform edges
            on_platform = False
            for platform in platforms:
                if (self.rect.bottom >= platform.rect.top and 
                    self.rect.bottom <= platform.rect.bottom and
                    self.rect.centerx >= platform.rect.left and 
                    self.rect.centerx <= platform.rect.right):
                    on_platform = True
                    break
            
            if not on_platform and self.rect.bottom < HEIGHT:
                self.direction *= -1
        
        elif self.enemy_type == "jumper":
            self.jump_timer += 1
            if self.jump_timer >= 120:  # Jump every 2 seconds
                self.rect.y -= 20
                self.jump_timer = 0
            else:
                self.rect.y += 1  # Fall down
        
        elif self.enemy_type == "flyer":
            self.rect.x += self.direction * self.speed
            self.rect.y = self.original_y + math.sin(pygame.time.get_ticks() * 0.01 + self.float_offset) * 20
            
            if self.rect.left <= 0 or self.rect.right >= WIDTH:
                self.direction *= -1

class PowerUp(pygame.sprite.Sprite):
    def __init__(self, x, y, power_type="health"):
        super().__init__()
        self.power_type = power_type
        self.image = pygame.Surface((20, 20))
        
        if power_type == "health":
            self.image.fill(GREEN)
        elif power_type == "speed":
            self.image.fill(YELLOW)
        elif power_type == "jump":
            self.image.fill(BLUE)
        
        self.rect = self.image.get_rect(center=(x, y))
        self.float_offset = random.uniform(0, math.pi * 2)
    
    def update(self):
        # Floating animation
        self.rect.y += math.sin(pygame.time.get_ticks() * 0.01 + self.float_offset) * 0.5
    
    def apply_effect(self, player):
        if self.power_type == "health":
            player.heal(30)
        elif self.power_type == "speed":
            player.max_speed = 8  # Temporary speed boost
        elif self.power_type == "jump":
            player.jump_power = -20  # Temporary jump boost

class Coin(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = pygame.Surface((20, 20))
        self.image.fill(YELLOW)
        self.rect = self.image.get_rect(center=(x, y))
        self.float_offset = random.uniform(0, math.pi * 2)
        self.collected = False
    
    def update(self):
        if not self.collected:
            # Floating and spinning animation
            self.rect.y += math.sin(pygame.time.get_ticks() * 0.01 + self.float_offset) * 0.5

class Game:
    def __init__(self):
        self.state = "menu"  # menu, playing, paused, game_over
        self.level = 1
        self.score = 0
        self.coins_collected = 0
        self.particles = ParticleSystem()
        self.camera_y = 0
        self.target_camera_y = 0
        
        # Game objects
        self.all_sprites = pygame.sprite.Group()
        self.platforms = pygame.sprite.Group()
        self.enemies = pygame.sprite.Group()
        self.coins = pygame.sprite.Group()
        self.power_ups = pygame.sprite.Group()
        
        self.player = None
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        self.setup_level()
    
    def setup_level(self):
        # Clear existing sprites
        self.all_sprites.empty()
        self.platforms.empty()
        self.enemies.empty()
        self.coins.empty()
        self.power_ups.empty()
        
        # Create player
        self.player = Player(100, HEIGHT - 100)
        self.all_sprites.add(self.player)
        
        # Generate level based on current level number
        self.generate_level()
    
    def generate_level(self):
        # Ground platforms
        for i in range(0, WIDTH, 100):
            platform = Platform(i, HEIGHT - 20, 100)
            self.all_sprites.add(platform)
            self.platforms.add(platform)
        
        # Generate platforms, enemies, and collectibles based on level
        platform_count = 8 + self.level * 2
        enemy_count = 3 + self.level
        coin_count = 10 + self.level * 3
        
        for i in range(platform_count):
            x = random.randint(50, WIDTH - 150)
            y = HEIGHT - 150 - i * 80
            width = random.randint(80, 200)
            
            platform_type = random.choice(["normal", "normal", "normal", "ice", "bouncy", "moving"])
            platform = Platform(x, y, width, platform_type)
            self.all_sprites.add(platform)
            self.platforms.add(platform)
            
            # Add coins on platforms
            if random.random() < 0.7:
                coin_x = x + width // 2
                coin_y = y - 30
                coin = Coin(coin_x, coin_y)
                self.all_sprites.add(coin)
                self.coins.add(coin)
        
        # Add enemies
        for i in range(enemy_count):
            x = random.randint(100, WIDTH - 100)
            y = HEIGHT - 200 - random.randint(0, 400)
            enemy_type = random.choice(["walker", "jumper", "flyer"])
            enemy = Enemy(x, y, enemy_type)
            self.all_sprites.add(enemy)
            self.enemies.add(enemy)
        
        # Add power-ups
        for i in range(3):
            x = random.randint(100, WIDTH - 100)
            y = HEIGHT - 200 - random.randint(0, 400)
            power_type = random.choice(["health", "speed", "jump"])
            power_up = PowerUp(x, y, power_type)
            self.all_sprites.add(power_up)
            self.power_ups.add(power_up)

    def handle_events(self, events):
        for event in events:
            if event.type == pygame.KEYDOWN:
                if self.state == "menu":
                    if event.key == pygame.K_SPACE:
                        self.state = "playing"
                        self.setup_level()
                elif self.state == "playing":
                    if event.key == pygame.K_SPACE:
                        self.player.jump()
                    elif event.key == pygame.K_LSHIFT or event.key == pygame.K_RSHIFT:
                        self.player.dash()
                    elif event.key == pygame.K_p:
                        self.state = "paused"
                elif self.state == "paused":
                    if event.key == pygame.K_p:
                        self.state = "playing"
                elif self.state == "game_over":
                    if event.key == pygame.K_r:
                        self.restart_game()
                    elif event.key == pygame.K_m:
                        self.state = "menu"

    def update(self):
        if self.state == "playing":
            # Update camera to follow player
            self.target_camera_y = max(0, self.player.rect.y - HEIGHT // 2)
            self.camera_y += (self.target_camera_y - self.camera_y) * 0.1

            # Update all sprites
            self.player.update(self.platforms, self.enemies, self.power_ups)

            for platform in self.platforms:
                platform.update()

            for enemy in self.enemies:
                enemy.update(self.platforms)

            for coin in self.coins:
                coin.update()

            for power_up in self.power_ups:
                power_up.update()

            # Check coin collection
            collected_coins = pygame.sprite.spritecollide(self.player, self.coins, True)
            for coin in collected_coins:
                self.coins_collected += 1
                self.score += 100
                self.particles.add_particles(coin.rect.centerx, coin.rect.centery, YELLOW)

            # Check if player died
            if self.player.health <= 0:
                self.state = "game_over"

            # Check level completion (collect all coins)
            if len(self.coins) == 0:
                self.level += 1
                self.score += 1000  # Level completion bonus
                self.setup_level()

            # Update particles
            self.particles.update()

    def draw(self):
        screen.fill((135, 206, 235))  # Sky blue background

        if self.state == "menu":
            self.draw_menu()
        elif self.state == "playing":
            self.draw_game()
        elif self.state == "paused":
            self.draw_game()
            self.draw_pause_overlay()
        elif self.state == "game_over":
            self.draw_game()
            self.draw_game_over()

    def draw_menu(self):
        # Title
        title_text = self.font.render("🦘 Pixel Jumper Adventure", True, BLACK)
        title_rect = title_text.get_rect(center=(WIDTH//2, HEIGHT//2 - 100))
        screen.blit(title_text, title_rect)

        # Instructions
        instructions = [
            "Arrow Keys / WASD - Move",
            "Space - Jump (Double Jump Available)",
            "Shift - Dash",
            "P - Pause",
            "Collect all coins to advance!",
            "",
            "Press SPACE to Start"
        ]

        y_offset = HEIGHT//2 - 50
        for instruction in instructions:
            text = self.small_font.render(instruction, True, BLACK)
            text_rect = text.get_rect(center=(WIDTH//2, y_offset))
            screen.blit(text, text_rect)
            y_offset += 30

    def draw_game(self):
        # Apply camera offset
        camera_offset = -self.camera_y

        # Draw all sprites with camera offset
        for sprite in self.all_sprites:
            draw_rect = sprite.rect.copy()
            draw_rect.y += camera_offset

            if isinstance(sprite, Player):
                sprite.draw(screen)
            else:
                screen.blit(sprite.image, draw_rect)

        # Draw particles
        self.particles.draw(screen)

        # Draw UI
        self.draw_ui()

    def draw_ui(self):
        # Score
        score_text = self.font.render(f"Score: {self.score}", True, BLACK)
        screen.blit(score_text, (10, 10))

        # Level
        level_text = self.font.render(f"Level: {self.level}", True, BLACK)
        screen.blit(level_text, (10, 50))

        # Coins
        coins_text = self.font.render(f"Coins: {self.coins_collected}", True, BLACK)
        screen.blit(coins_text, (10, 90))

        # Health bar
        health_width = 200
        health_height = 20
        health_x = WIDTH - health_width - 10
        health_y = 10

        # Background
        pygame.draw.rect(screen, RED, (health_x, health_y, health_width, health_height))
        # Health
        health_fill = (self.player.health / self.player.max_health) * health_width
        pygame.draw.rect(screen, GREEN, (health_x, health_y, health_fill, health_height))
        # Border
        pygame.draw.rect(screen, BLACK, (health_x, health_y, health_width, health_height), 2)

        # Health text
        health_text = self.small_font.render(f"Health: {self.player.health}/{self.player.max_health}", True, BLACK)
        screen.blit(health_text, (health_x, health_y + 25))

        # Dash cooldown indicator
        if self.player.dash_cooldown > 0:
            dash_text = self.small_font.render(f"Dash: {self.player.dash_cooldown//60 + 1}s", True, RED)
        else:
            dash_text = self.small_font.render("Dash: Ready", True, GREEN)
        screen.blit(dash_text, (WIDTH - 150, 60))

    def draw_pause_overlay(self):
        # Semi-transparent overlay
        overlay = pygame.Surface((WIDTH, HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        screen.blit(overlay, (0, 0))

        # Pause text
        pause_text = self.font.render("PAUSED", True, WHITE)
        pause_rect = pause_text.get_rect(center=(WIDTH//2, HEIGHT//2))
        screen.blit(pause_text, pause_rect)

        resume_text = self.small_font.render("Press P to Resume", True, WHITE)
        resume_rect = resume_text.get_rect(center=(WIDTH//2, HEIGHT//2 + 50))
        screen.blit(resume_text, resume_rect)

    def draw_game_over(self):
        # Semi-transparent overlay
        overlay = pygame.Surface((WIDTH, HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        screen.blit(overlay, (0, 0))

        # Game over text
        game_over_text = self.font.render("GAME OVER", True, RED)
        game_over_rect = game_over_text.get_rect(center=(WIDTH//2, HEIGHT//2 - 50))
        screen.blit(game_over_text, game_over_rect)

        # Final score
        final_score_text = self.small_font.render(f"Final Score: {self.score}", True, WHITE)
        final_score_rect = final_score_text.get_rect(center=(WIDTH//2, HEIGHT//2))
        screen.blit(final_score_text, final_score_rect)

        # Level reached
        level_text = self.small_font.render(f"Level Reached: {self.level}", True, WHITE)
        level_rect = level_text.get_rect(center=(WIDTH//2, HEIGHT//2 + 30))
        screen.blit(level_text, level_rect)

        # Instructions
        restart_text = self.small_font.render("Press R to Restart or M for Menu", True, WHITE)
        restart_rect = restart_text.get_rect(center=(WIDTH//2, HEIGHT//2 + 80))
        screen.blit(restart_text, restart_rect)

    def restart_game(self):
        self.level = 1
        self.score = 0
        self.coins_collected = 0
        self.camera_y = 0
        self.target_camera_y = 0
        self.state = "playing"
        self.setup_level()

# Global game instance
game = Game()

async def main():
    global game

    while True:
        events = pygame.event.get()

        # Handle quit
        for event in events:
            if event.type == pygame.QUIT:
                pygame.quit()
                return

        # Handle game events
        game.handle_events(events)

        # Update game
        game.update()

        # Draw game
        game.draw()

        pygame.display.flip()
        await asyncio.sleep(1.0 / FPS)

# Run game
if platform.system() == "Emscripten":
    asyncio.ensure_future(main())
else:
    if __name__ == "__main__":
        asyncio.run(main())
